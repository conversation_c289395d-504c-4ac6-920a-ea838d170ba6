#!/usr/bin/env python3
"""
Test CLIPScore implementation
"""

import sys
import os
import logging
import torch
from PIL import Image
from transformers import CLIPProcessor, CLIPModel

sys.path.append('/work/tesi_ediluzio')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_clip_score():
    """Test CLIPScore con un esempio reale"""

    # Esempio di test
    image_path = "/work/tesi_ediluzio/data/processed/xml_format_optimized/baseline_t7_images_full/unknown_sr_051299_dup_4552.png"
    text = "The image depicts a simple, black and white illustration of an ambulance"

    if not os.path.exists(image_path):
        logger.error(f"❌ Immagine non trovata: {image_path}")
        return

    logger.info(f"🧪 Test CLIPScore...")
    logger.info(f"   Immagine: {os.path.basename(image_path)}")
    logger.info(f"   Testo: {text}")

    try:
        # Carica CLIP direttamente
        logger.info("📥 Caricamento CLIP model...")
        model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32")
        processor = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32")

        # Carica immagine
        image = Image.open(image_path).convert('RGB')

        # Processa input
        inputs = processor(text=[text], images=[image], return_tensors="pt", padding=True)

        # Calcola similarità
        with torch.no_grad():
            outputs = model(**inputs)
            logits_per_image = outputs.logits_per_image
            score = torch.sigmoid(logits_per_image).item()

        logger.info(f"✅ CLIPScore: {score:.4f}")

    except Exception as e:
        logger.error(f"❌ Errore test CLIPScore: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_clip_score()
